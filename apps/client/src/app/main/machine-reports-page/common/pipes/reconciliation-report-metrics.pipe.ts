import { Pipe, PipeTransform } from '@angular/core';
import { ReconciliationReportMetrics } from '@cms/machine-metrics';

@Pipe({
    name: 'reconciliationReportMetricsPipe',
    standalone: true
})
export class ReconciliationReportMetricsPipePipe implements PipeTransform {
    transform(value: string, ..._args: unknown[]): string {
        return ReconciliationReportMetricsPipePipe.stringToDescription(value);
    }

    // eslint-disable-next-line consistent-return
    static stringToDescription(value: string): string {
        switch (value as ReconciliationReportMetrics) {
            case ReconciliationReportMetrics.AccountingTotalWin:
                return $localize`Win Accounting`;
            case ReconciliationReportMetrics.CashRevenue:
                return $localize`Cash Revenue`;
            case ReconciliationReportMetrics.TicketRevenue:
                return $localize`Ticket Revenue`;
            case ReconciliationReportMetrics.TransferRevenue:
                return $localize`Transfer Revenue`;
            case ReconciliationReportMetrics.AccountingCreditsIn:
                return $localize`Credits In`;
            case ReconciliationReportMetrics.TotalRevenue:
                return $localize`Total Revenue`;
            case ReconciliationReportMetrics.TicketExpenses:
                return $localize`Ticket Expenses`;
            case ReconciliationReportMetrics.TransferExpenses:
                return $localize`Transfer Expenses`;
            case ReconciliationReportMetrics.AccountingHandpays:
                return $localize`Handpays`;
            case ReconciliationReportMetrics.AccountingCreditsOut:
                return $localize`Credits Out`;
            case ReconciliationReportMetrics.AccountingTotalOut:
                return $localize`Total Out`;
            case ReconciliationReportMetrics.PlayTotalWin:
                return $localize`Win Play Meters`;
            case ReconciliationReportMetrics.Turnover:
                return $localize`Turnover`;
            case ReconciliationReportMetrics.PaidToMachine:
                return $localize`Paid To Machine`;
            case ReconciliationReportMetrics.Jackpots:
                return $localize`Jackpots`;
            case ReconciliationReportMetrics.Progressives:
                return $localize`Progressives`;
            case ReconciliationReportMetrics.FinanceTotalWin:
                return $localize`Win Finance Meters`;
            case ReconciliationReportMetrics.FinanceCashIn:
                return $localize`Cash In`;
            case ReconciliationReportMetrics.FinanceTicketsIn:
                return $localize`Tickets In`;
            case ReconciliationReportMetrics.FinanceTransfersIn:
                return $localize`Transfers In`;
            case ReconciliationReportMetrics.FinanceCreditsIn:
                return $localize`Credits In`;
            case ReconciliationReportMetrics.FinanceTotalIn:
                return $localize`Total In`;
            case ReconciliationReportMetrics.FinanceTicketsOut:
                return $localize`Tickets Out`;
            case ReconciliationReportMetrics.FinanceTransfersOut:
                return $localize`Transfers Out`;
            case ReconciliationReportMetrics.FinanceHandpays:
                return $localize`Handpays`;
            case ReconciliationReportMetrics.FinanceCreditsOut:
                return $localize`Credits Out`;
            case ReconciliationReportMetrics.FinanceTotalOut:
                return $localize`Total Out`;
            case ReconciliationReportMetrics.Variance:
                return $localize`Variance`;
        }
    }
}
