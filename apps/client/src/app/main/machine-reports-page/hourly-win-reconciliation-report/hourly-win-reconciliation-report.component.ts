import { CommonModule } from '@angular/common';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { Title } from '@angular/platform-browser';
import {
    MachineWinReconciliationReportItem,
    MachineWinReconciliationReportMetrics,
    ReconciliationReportMetrics,
    WinReconciliationHourlyRequest,
    WinReconciliationReportType
} from '@cms/machine-metrics';
import { ErrorUtil, SettingsService, TgLoggerFactory, greaterThanOtherControlValidator } from '@tronius/frontend-common';
import {
    TgArrayDataSource,
    TgBrowserStorageColumnsSource,
    TgDateModule,
    TgLoadingOverlayComponent,
    TgMainPanelModule,
    TgTableModule,
    createColumnSort
} from '@tronius/frontend-ui';
import { formatDate, subMilliseconds } from 'date-fns';
import { firstValueFrom } from 'rxjs';

import { BackendService } from '../../../core/backend/backend.service';
import { ReportFormsService } from '../../../core/services/report-forms.service';
import { ReportTypePipe } from '../common/pipes/report-type.pipe';
import { WinReconciliationReportTypePipe } from '../common/pipes/win-reconciliation-report-type.pipe';
import { ReportType } from '../common/types/report-type.enum';
import { MachineWinReconciliationReportRow, TotalWinReconciliationReportRow } from '../common/win-reconciliation-report-data-source.model';
import { getWinReconciliationReportColumnsSource } from '../common/win-reconciliation-report.columns';
import { ReconciliationAmountIconComponent } from '../reconciliation-amount-icon/reconciliation-amount-icon.component';

interface FormGroupType {
    type: FormControl<ReportType>;
    promo: FormControl<WinReconciliationReportType>;
    gamingDay: FormControl<Date>;
    startHourIndex: FormControl<number>;
    endHourIndex: FormControl<number>;
}

@Component({
    selector: 'app-hourly-win-reconciliation-report',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatInputModule,
        MatButtonModule,
        MatIconModule,
        MatDatepickerModule,
        MatSelectModule,
        MatSnackBarModule,
        ReconciliationAmountIconComponent,
        ReportTypePipe,
        TgLoadingOverlayComponent,
        TgDateModule,
        TgMainPanelModule,
        TgTableModule,
        WinReconciliationReportTypePipe
    ],
    templateUrl: './hourly-win-reconciliation-report.component.html',
    styleUrls: ['./hourly-win-reconciliation-report.component.scss']
})
export class HourlyWinReconciliationReportComponent implements OnInit, OnDestroy {
    protected readonly ReconciliationReportMetrics = ReconciliationReportMetrics;

    protected readonly reportTypesArray = Object.values(ReportType);
    protected readonly promoInclusionTypes = Object.values(WinReconciliationReportType);

    protected startHours: Array<{ value: number; label: string }> = [];
    protected endHours: Array<{ value: number; label: string }> = [];

    protected form!: FormGroup<FormGroupType>;
    protected requestData!: typeof this.form.value;

    protected columnsSourceMachines?: TgBrowserStorageColumnsSource;
    protected dataSourceMachines!: TgArrayDataSource<MachineWinReconciliationReportItem>;
    protected columnsSourceTotal?: TgBrowserStorageColumnsSource;
    protected dataSourceTotal!: TgArrayDataSource<TotalWinReconciliationReportRow>;

    protected readonly operatorName: string;
    protected generatedBy!: string;
    protected generatedTime = new Date();
    protected isLoading = false;

    private readonly logger = TgLoggerFactory.getLogger(HourlyWinReconciliationReportComponent.name);

    constructor(
        private readonly backendService: BackendService,
        private readonly snackBar: MatSnackBar,
        private readonly reportFormsService: ReportFormsService,
        private readonly settingsService: SettingsService,
        private readonly titleService: Title
    ) {
        const title = $localize`Machine Hourly Win Reconciliation Report`;
        this.generatedBy = this.reportFormsService.determineGeneratedBy();
        this.titleService.setTitle(`${title} (${this.generatedBy})`);
        this.operatorName = this.settingsService.getSettings().propertyName;
        this.form = this.initForm();
        this.requestData = this.form.value;
    }

    ngOnInit(): void {
        this.startHours = this.reportFormsService.generateHourSelectorOptions(true);
        this.endHours = this.reportFormsService.generateHourSelectorOptions(false);
        this.generateReport();
    }

    ngOnDestroy(): void {
        this.titleService.setTitle('CMS');
    }

    protected generateReport(): void {
        this.determineColumnsSource();
        void this.getReport();
    }

    protected print(): void {
        window.print();
    }

    private initForm(): FormGroup<FormGroupType> {
        const gamingDay = this.reportFormsService.getCurrentGamingDayData().date;

        const form = new FormGroup({
            type: new FormControl<ReportType>(ReportType.Win, { nonNullable: true, validators: [Validators.required] }),
            promo: new FormControl<WinReconciliationReportType>(WinReconciliationReportType.IncludePromo, {
                nonNullable: true,
                validators: [Validators.required]
            }),
            gamingDay: new FormControl<Date>(new Date(gamingDay), { nonNullable: true, validators: [Validators.required] }),
            startHourIndex: new FormControl<number>(0, { nonNullable: true, validators: [Validators.required] }),
            // eslint-disable-next-line @typescript-eslint/no-magic-numbers
            endHourIndex: new FormControl<number>(23, { nonNullable: true, validators: [Validators.required] })
        });

        form.controls.endHourIndex.addValidators(greaterThanOtherControlValidator(form.controls.startHourIndex, true));

        return form;
    }

    private determineColumnsSource(): void {
        const { type } = this.form.getRawValue();

        const columns = type === ReportType.Full ? 'full' : 'win';
        this.columnsSourceMachines = getWinReconciliationReportColumnsSource('hourly', columns, 'machines');
        this.columnsSourceTotal = getWinReconciliationReportColumnsSource('hourly', columns, 'total');
    }

    private async getReport(): Promise<void> {
        try {
            this.isLoading = true;

            const machinesReportRows = await this.getReportMachinesRows();

            this.dataSourceMachines = new TgArrayDataSource<MachineWinReconciliationReportItem>(
                machinesReportRows,
                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                createColumnSort(this.columnsSourceMachines!)
            );
            this.dataSourceTotal = new TgArrayDataSource<TotalWinReconciliationReportRow>([
                this.createTotalRowFromMachineRows(machinesReportRows)
            ]);
        } catch (error) {
            const errorMessage = ErrorUtil.extractErrorMessage(error);
            this.logger.error('Failed to load hourly win reconciliation report', errorMessage);
            this.snackBar.open($localize`Failed to load hourly win reconciliation report`, $localize`Close`);
        } finally {
            this.isLoading = false;
        }
    }

    private async getReportMachinesRows(): Promise<MachineWinReconciliationReportRow[]> {
        const { gamingDay, promo, startHourIndex, endHourIndex } = (this.requestData = this.form.getRawValue());

        const gamingDayString = formatDate(gamingDay, 'yyyy-MM-dd');

        const start = this.reportFormsService.createDateFromGamingDayAndHourIndex(gamingDayString, startHourIndex);
        const end = subMilliseconds(this.reportFormsService.createDateFromGamingDayAndHourIndex(gamingDayString, endHourIndex + 1), 1);

        const request = WinReconciliationHourlyRequest.create({ from: start, to: end, type: promo });

        const request$ = this.backendService.getHourlyMachineWinReconciliationReport(request);
        const requestItems = await firstValueFrom(request$);
        const reportRows = requestItems.map((report) => this.transformMachineReconciliationReport(report));

        return reportRows;
    }

    private createTotalRowFromMachineRows(machineRows: MachineWinReconciliationReportRow[]): TotalWinReconciliationReportRow {
        const initialMetrics: MachineWinReconciliationReportMetrics = {};
        Object.values(ReconciliationReportMetrics).forEach((metricKey) => {
            initialMetrics[metricKey] = 0;
        });

        const totalsRow = machineRows.reduce<TotalWinReconciliationReportRow>(
            (accumulator, row) => {
                Object.entries(row.metrics).forEach(([key, value]) => {
                    const metricKey = key as ReconciliationReportMetrics;
                    const currentValue = accumulator.metrics[metricKey] || 0;
                    accumulator.metrics[metricKey] = currentValue + (value || 0);
                });
                accumulator.variance = (accumulator.variance || 0) + (row.variance || 0);

                return accumulator;
            },
            { metrics: initialMetrics, variance: 0 }
        );

        return totalsRow;
    }

    private transformMachineReconciliationReport(report: MachineWinReconciliationReportItem): MachineWinReconciliationReportRow {
        const { machine, metrics } = report;

        const machineLabel = `${machine.location} (${machine.assetNumber})`;

        const accountingTotalWin = metrics[ReconciliationReportMetrics.AccountingTotalWin];
        const playTotalWin = metrics[ReconciliationReportMetrics.PlayTotalWin];
        const financeTotalWin = metrics[ReconciliationReportMetrics.FinanceTotalWin];

        const variance1 = accountingTotalWin && playTotalWin ? Math.abs(accountingTotalWin - playTotalWin) : 0;
        const variance2 = accountingTotalWin && financeTotalWin ? Math.abs(accountingTotalWin - financeTotalWin) : 0;
        const variance3 = playTotalWin && financeTotalWin ? Math.abs(playTotalWin - financeTotalWin) : 0;

        const variance = Math.max(variance1, variance2, variance3);

        return { ...report, machineLabel, variance };
    }
}
