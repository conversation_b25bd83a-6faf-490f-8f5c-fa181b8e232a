import { JobQueueModule } from '@cms/job-queue';
import { UpsertDailyMetricsJob } from '@cms/machine-metrics';
import { Module } from '@nestjs/common';

import { MachineDailyMetricsController } from './controllers';
import { ScheduleNextGamingDayOnMetricsUpsertedHandler, UpsertDailyMetricsOnSnapshotsAddedHandler } from './handlers';
import { ExtraDependenciesProvider, GamingDayProvider, MachineMetersProvider, MachineProvider } from './providers';
import { DailyWinReconciliationReportService, MachineDailyMetricsService } from './services';
import { UpsertDailyMetricsWorker } from './workers';

@Module({
    imports: [JobQueueModule.forJob(UpsertDailyMetricsJob)],
    controllers: [MachineDailyMetricsController],
    providers: [
        MachineMetersProvider,
        MachineProvider,
        ExtraDependenciesProvider,
        GamingDayProvider,
        MachineDailyMetricsService,
        UpsertDailyMetricsWorker,
        UpsertDailyMetricsOnSnapshotsAddedHandler,
        ScheduleNextGamingDayOnMetricsUpsertedHandler,
        DailyWinReconciliationReportService
    ]
})
export class MachineDailyMetricsDomainModule {}
