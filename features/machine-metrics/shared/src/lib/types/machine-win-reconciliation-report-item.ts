import { deserialize } from '@cms/serialization';
import { MachineProfileListItem } from '@tronius/shared-domain';
import { Type } from 'class-transformer';
import { IsNotEmpty, IsObject, ValidateNested } from 'class-validator';
import { orderBy } from 'lodash';

import { ReconciliationReportMetrics } from './reconciliation-report-metrics.enum';

export type MachineWinReconciliationReportMetrics = Partial<Record<ReconciliationReportMetrics, number | null>>;

export interface MachineWinReconciliationReportItemProps {
    machine: MachineProfileListItem;
    metrics: MachineWinReconciliationReportMetrics;
}

export class MachineWinReconciliationReportItem implements MachineWinReconciliationReportItemProps {
    @Type(() => MachineProfileListItem)
    @ValidateNested()
    @IsNotEmpty()
    machine!: MachineProfileListItem;

    // TODO: validate report fields
    @IsObject()
    @IsNotEmpty()
    metrics!: MachineWinReconciliationReportMetrics;

    static create(props: MachineWinReconciliationReportItemProps): MachineWinReconciliationReportItem {
        return deserialize(MachineWinReconciliationReportItem, props);
    }

    static calculateVariance(metrics: MachineWinReconciliationReportMetrics): number | null {
        const accountingTotalWin = metrics[ReconciliationReportMetrics.AccountingTotalWin];
        const playTotalWin = metrics[ReconciliationReportMetrics.PlayTotalWin];
        const financeTotalWin = metrics[ReconciliationReportMetrics.FinanceTotalWin];

        const valueIsMissing = [accountingTotalWin, playTotalWin, financeTotalWin].some((value) => {
            return typeof value !== 'number';
        });

        if (valueIsMissing) {
            return null;
        }

        const variance1 = Math.abs(accountingTotalWin - playTotalWin);
        const variance2 = Math.abs(accountingTotalWin - financeTotalWin);
        const variance3 = Math.abs(playTotalWin - financeTotalWin);

        const variance = Math.max(variance1, variance2, variance3);
        return variance;
    }

    static sortReportItems(reportItems: MachineWinReconciliationReportItem[]): MachineWinReconciliationReportItem[] {
        // First, create a function to determine if variance is null (for sorting nulls last)
        const hasVariance = (item: MachineWinReconciliationReportItem): number => {
            return item.metrics.variance === null ? 0 : 1;
        };

        // Function to get absolute variance value (for sorting by magnitude)
        const absVariance = (item: MachineWinReconciliationReportItem): number => {
            const { variance } = item.metrics;
            return typeof variance === 'number' ? Math.abs(variance) : 0;
        };

        // Function to get location for final sorting
        const location = (item: MachineWinReconciliationReportItem): string => {
            return item.machine.location;
        };

        // Use orderBy with multiple sorting criteria
        return orderBy(reportItems, [hasVariance, absVariance, location], ['desc', 'desc', 'asc']);
    }
}
