/** The metrics of the reconciliation report */
export enum ReconciliationReportMetrics {
    AccountingTotalWin = 'accounting-total-win',
    CashRevenue = 'cash-revenue',
    TicketRevenue = 'ticket-revenue',
    TransferRevenue = 'transfer-revenue',
    AccountingCreditsIn = 'accounting-credits-in',
    TotalRevenue = 'total-revenue',
    TicketExpenses = 'ticket-expenses',
    TransferExpenses = 'transfer-expenses',
    AccountingHandpays = 'accounting-handpays',
    AccountingCreditsOut = 'accounting-credits-out',
    AccountingTotalOut = 'accounting-total-out',
    PlayTotalWin = 'play-total-win',
    Turnover = 'turnover',
    PaidToMachine = 'paid-to-machine',
    Jackpots = 'jackpots',
    Progressives = 'progressives',
    FinanceTotalWin = 'finance-total-win',
    FinanceCashIn = 'finance-cash-in',
    FinanceTicketsIn = 'finance-tickets-in',
    FinanceTransfersIn = 'finance-transfers-in',
    FinanceCreditsIn = 'finance-credits-in',
    FinanceTotalIn = 'finance-total-in',
    FinanceTicketsOut = 'finance-tickets-out',
    FinanceTransfersOut = 'finance-transfers-out',
    FinanceHandpays = 'finance-handpays',
    FinanceCreditsOut = 'finance-credits-out',
    FinanceTotalOut = 'finance-total-out',
    Variance = 'variance'
}
